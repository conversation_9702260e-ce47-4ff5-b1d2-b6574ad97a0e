import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Search,
  Filter,
  Grid,
  List,
  TrendingUp,
  Clock,
  User,
  Eye,
  MessageSquare,
  BookOpen,
  FileText,
  Calendar,
  ArrowRight,
  Star,
  Bookmark
} from 'lucide-react';
import AuthHeader from '@/components/AuthHeader';
import Footer from '@/components/Footer';
import ScrollToTop from '@/components/ScrollToTop';
import HomepageSwitcher from '@/components/ui/homepage-switcher';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { contentApi, BlogPost, Note } from '@/services/api';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { getOptimizedImageUrl, handleImageError } from '@/utils/imageUtils';

const Home2 = () => {
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('latest');
  
  // Content state
  const [featuredContent, setFeaturedContent] = useState<BlogPost | null>(null);
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [notes, setNotes] = useState<Note[]>([]);
  const [trendingContent, setTrendingContent] = useState<BlogPost[]>([]);
  
  const { toast } = useToast();

  useEffect(() => {
    loadContent();
  }, []);

  const loadContent = async () => {
    try {
      setLoading(true);
      
      // Load featured content and recent posts
      const [blogsResponse, notesResponse] = await Promise.all([
        contentApi.getBlogPosts({ limit: 20, status: 'Active' }),
        contentApi.getNotes({ limit: 10, status: 'Active' })
      ]);

      const blogs = blogsResponse.blog_posts || [];
      const notesData = notesResponse.notes || [];

      // Set featured content (first featured blog or first blog)
      const featured = blogs.find(blog => blog.is_featured) || blogs[0];
      setFeaturedContent(featured);

      // Set other content
      setBlogPosts(blogs.filter(blog => blog.content_id !== featured?.content_id));
      setNotes(notesData);
      
      // Set trending content (top 6 most viewed/recent)
      setTrendingContent(blogs.slice(0, 6));

    } catch (error) {
      console.error('Error loading content:', error);
      toast({
        title: "Error",
        description: "Failed to load content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter and sort content
  const getFilteredContent = () => {
    let allContent: any[] = [];
    
    if (selectedCategory === 'all' || selectedCategory === 'blogs') {
      allContent = [...allContent, ...blogPosts.map(blog => ({ ...blog, type: 'blog' }))];
    }
    
    if (selectedCategory === 'all' || selectedCategory === 'notes') {
      allContent = [...allContent, ...notes.map(note => ({ ...note, type: 'note' }))];
    }

    // Apply search filter
    if (searchQuery) {
      allContent = allContent.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.summary && item.summary.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply sorting
    switch (sortBy) {
      case 'latest':
        allContent.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case 'popular':
        allContent.sort((a, b) => (b.view_count || 0) - (a.view_count || 0));
        break;
      case 'alphabetical':
        allContent.sort((a, b) => a.title.localeCompare(b.title));
        break;
    }

    return allContent;
  };

  const renderContentCard = (item: any, type: string) => {
    const isNote = type === 'note';
    const linkTo = isNote ? `/notes/${item.note_id}` : `/blogs/${item.content_id}`;
    
    if (viewMode === 'list') {
      return (
        <Card key={`${type}-${item.content_id || item.note_id}`} className="group hover:shadow-md transition-all duration-200">
          <Link to={linkTo} className="block">
            <CardContent className="p-4">
              <div className="flex gap-4">
                {/* Thumbnail */}
                <div className="flex-shrink-0 w-24 h-16 rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={getOptimizedImageUrl(item.featured_image, 200, 120, type)}
                    alt={item.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => handleImageError(e, type)}
                  />
                </div>
                
                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary" className="text-xs">
                      {isNote ? 'Note' : 'Article'}
                    </Badge>
                    {item.category && (
                      <Badge variant="outline" className="text-xs">
                        {item.category}
                      </Badge>
                    )}
                  </div>
                  
                  <h3 className="font-semibold text-sm line-clamp-1 group-hover:text-lawvriksh-navy transition-colors">
                    {item.title}
                  </h3>
                  
                  {item.summary && (
                    <p className="text-xs text-gray-600 line-clamp-2 mt-1">
                      {item.summary}
                    </p>
                  )}
                  
                  <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                    <span className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {item.author_name || 'Anonymous'}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {format(new Date(item.created_at), 'MMM dd, yyyy')}
                    </span>
                    {item.view_count && (
                      <span className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        {item.view_count}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Link>
        </Card>
      );
    }

    // Grid view
    return (
      <Card key={`${type}-${item.content_id || item.note_id}`} className="group hover:shadow-lg transition-all duration-300 h-full">
        <Link to={linkTo} className="block h-full">
          {/* Thumbnail */}
          <div className="aspect-video overflow-hidden rounded-t-lg bg-gray-100">
            <img
              src={getOptimizedImageUrl(item.featured_image, 400, 225, type)}
              alt={item.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
              onError={(e) => handleImageError(e, type)}
            />
          </div>
          
          <CardContent className="p-4 flex flex-col h-full">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="secondary" className="text-xs">
                {isNote ? 'Note' : 'Article'}
              </Badge>
              {item.category && (
                <Badge variant="outline" className="text-xs">
                  {item.category}
                </Badge>
              )}
            </div>
            
            <h3 className="font-semibold text-sm line-clamp-2 group-hover:text-lawvriksh-navy transition-colors mb-2">
              {item.title}
            </h3>
            
            {item.summary && (
              <p className="text-xs text-gray-600 line-clamp-3 mb-3 flex-1">
                {item.summary}
              </p>
            )}
            
            <div className="flex items-center justify-between text-xs text-gray-500 mt-auto">
              <span className="flex items-center gap-1">
                <User className="h-3 w-3" />
                {item.author_name || 'Anonymous'}
              </span>
              <span>{format(new Date(item.created_at), 'MMM dd')}</span>
            </div>
          </CardContent>
        </Link>
      </Card>
    );
  };

  const filteredContent = getFilteredContent();

  return (
    <div className="min-h-screen bg-white">
      <AuthHeader />
      
      <main className="pt-20">
        {/* Content Hub Header */}
        <section className="bg-gradient-to-br from-lawvriksh-navy/5 to-lawvriksh-burgundy/5 py-12">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8">
              <h1 className="legal-heading text-3xl sm:text-4xl font-bold text-lawvriksh-navy mb-4">
                Legal Content Hub
              </h1>
              <p className="legal-text text-lg text-gray-600 max-w-2xl mx-auto">
                Explore our comprehensive collection of legal articles, research papers, and insights. 
                Everything you need in one place.
              </p>
            </div>

            {/* Search and Filters */}
            <div className="max-w-4xl mx-auto">
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                {/* Search */}
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search articles, notes, and more..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Category Filter */}
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Content</SelectItem>
                    <SelectItem value="blogs">Articles</SelectItem>
                    <SelectItem value="notes">Notes</SelectItem>
                  </SelectContent>
                </Select>

                {/* Sort */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="latest">Latest</SelectItem>
                    <SelectItem value="popular">Most Popular</SelectItem>
                    <SelectItem value="alphabetical">A-Z</SelectItem>
                  </SelectContent>
                </Select>

                {/* View Mode */}
                <div className="flex border rounded-lg">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Content Section */}
        {featuredContent && !loading && (
          <section className="py-12 bg-white">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-between mb-8">
                <h2 className="legal-heading text-2xl font-bold text-lawvriksh-navy flex items-center gap-2">
                  <Star className="h-6 w-6 text-lawvriksh-gold" />
                  Featured Content
                </h2>
              </div>

              <Card className="group cursor-pointer transition-all duration-300 hover:shadow-xl border-2 border-gray-200 hover:border-lawvriksh-navy/30">
                <Link to={`/blogs/${featuredContent.content_id}`} className="block">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                    {/* Featured Image */}
                    <div className="relative aspect-[16/9] lg:aspect-square overflow-hidden">
                      <img
                        src={getOptimizedImageUrl(featuredContent.featured_image, 600, 400, 'blog')}
                        alt={featuredContent.title}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                        onError={(e) => handleImageError(e, 'blog')}
                      />
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-lawvriksh-gold text-lawvriksh-navy font-semibold">
                          Featured
                        </Badge>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-8 flex flex-col justify-center">
                      <div className="flex items-center gap-2 mb-4">
                        <Badge variant="secondary">
                          {featuredContent.category || 'Article'}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {format(new Date(featuredContent.created_at), 'MMMM dd, yyyy')}
                        </span>
                      </div>

                      <h3 className="legal-heading text-2xl font-bold text-lawvriksh-navy mb-4 group-hover:text-lawvriksh-burgundy transition-colors">
                        {featuredContent.title}
                      </h3>

                      {featuredContent.summary && (
                        <p className="legal-text text-gray-600 mb-6 line-clamp-3">
                          {featuredContent.summary}
                        </p>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            {featuredContent.author_name}
                          </span>
                          {featuredContent.comment_count > 0 && (
                            <span className="flex items-center gap-1">
                              <MessageSquare className="h-4 w-4" />
                              {featuredContent.comment_count}
                            </span>
                          )}
                        </div>

                        <Button variant="ghost" className="text-lawvriksh-navy hover:text-lawvriksh-burgundy">
                          Read More
                          <ArrowRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Link>
              </Card>
            </div>
          </section>
        )}

        {/* Main Content Grid */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Main Content Area */}
              <div className="lg:col-span-3">
                {loading ? (
                  <div className={cn(
                    viewMode === 'grid'
                      ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                      : "space-y-4"
                  )}>
                    {Array.from({ length: 9 }).map((_, i) => (
                      <Card key={i} className="h-64">
                        <Skeleton className="w-full h-32" />
                        <CardContent className="p-4">
                          <Skeleton className="h-4 w-3/4 mb-2" />
                          <Skeleton className="h-3 w-full mb-1" />
                          <Skeleton className="h-3 w-2/3" />
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : filteredContent.length > 0 ? (
                  <div className={cn(
                    viewMode === 'grid'
                      ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                      : "space-y-4"
                  )}>
                    {filteredContent.map((item) => renderContentCard(item, item.type))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">No content found</h3>
                    <p className="text-gray-500">
                      {searchQuery
                        ? `No results found for "${searchQuery}"`
                        : "No content available in this category"
                      }
                    </p>
                  </div>
                )}
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                {/* Trending Content */}
                <Card className="mb-6">
                  <CardHeader>
                    <h3 className="legal-heading text-lg font-semibold text-lawvriksh-navy flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      Trending Now
                    </h3>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {loading ? (
                      Array.from({ length: 5 }).map((_, i) => (
                        <div key={i} className="flex gap-3">
                          <Skeleton className="w-16 h-12 rounded" />
                          <div className="flex-1">
                            <Skeleton className="h-3 w-full mb-1" />
                            <Skeleton className="h-3 w-2/3" />
                          </div>
                        </div>
                      ))
                    ) : (
                      trendingContent.slice(0, 5).map((item, index) => (
                        <Link
                          key={item.content_id}
                          to={`/blogs/${item.content_id}`}
                          className="flex gap-3 group hover:bg-gray-50 p-2 rounded-lg transition-colors"
                        >
                          <div className="flex-shrink-0 w-16 h-12 rounded overflow-hidden bg-gray-100">
                            <img
                              src={getOptimizedImageUrl(item.featured_image, 100, 75, 'blog')}
                              alt={item.title}
                              className="w-full h-full object-cover"
                              onError={(e) => handleImageError(e, 'blog')}
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-1 mb-1">
                              <span className="text-xs font-bold text-lawvriksh-gold">
                                #{index + 1}
                              </span>
                              <Badge variant="outline" className="text-xs">
                                {item.category || 'Article'}
                              </Badge>
                            </div>
                            <h4 className="text-xs font-medium line-clamp-2 group-hover:text-lawvriksh-navy transition-colors">
                              {item.title}
                            </h4>
                            <p className="text-xs text-gray-500 mt-1">
                              {format(new Date(item.created_at), 'MMM dd')}
                            </p>
                          </div>
                        </Link>
                      ))
                    )}
                  </CardContent>
                </Card>

                {/* Quick Stats */}
                <Card>
                  <CardHeader>
                    <h3 className="legal-heading text-lg font-semibold text-lawvriksh-navy">
                      Content Stats
                    </h3>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        Total Articles
                      </span>
                      <span className="font-semibold text-lawvriksh-navy">
                        {blogPosts.length + (featuredContent ? 1 : 0)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Notes
                      </span>
                      <span className="font-semibold text-lawvriksh-navy">
                        {notes.length}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Updated Today
                      </span>
                      <span className="font-semibold text-lawvriksh-navy">
                        {filteredContent.filter(item => {
                          const today = new Date();
                          const itemDate = new Date(item.created_at);
                          return itemDate.toDateString() === today.toDateString();
                        }).length}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
      <ScrollToTop />
      <HomepageSwitcher />
    </div>
  );
};

export default Home2;
